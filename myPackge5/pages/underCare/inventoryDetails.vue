<template>
    <view class="page-container">
        <CustomNavbar :title="'详情'" :titleColor="'##333333'" />
        <view class="header">
            <view class="fifter static-title">养殖场详情</view>
            <view class="fifter">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>

        <view class="inventory-container">
            <scroll-view
                class="inventory-list"
                scroll-y="true"
                :scroll-with-animation="true"
                @scrolltolower="scrollToLower"
                :refresher-enabled="true"
                :refresher-triggered="refresherState"
                @refresherrefresh="bindrefresherrefresh"
            >
                <view class="list-content">
                    <view v-for="(item, index) in tableData" :key="index" class="list-item" @click="handleRowClick(item, index)">
                        <view class="item-header">
                            <text class="item-time">{{ item.earTagNo }}</text>
                        </view>
                        <view class="item-content">
                            <view class="content-row">
                                <text class="content-label">品种：</text>
                                <text class="content-value">{{ item.varietiesName || '-' }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">栏位：</text>
                                <text class="content-value">{{ item.penName || '-' }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">体重：</text>
                                <text class="content-value">{{ item.livestockWeight }}kg</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">入库批次：</text>
                                <text class="content-value">{{ item.batch || '-' }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">健康状况：</text>
                                <text class="content-value" :class="getHealthStatusClass(item.healthStatus)">{{ item.healthStatusText }}</text>
                            </view>
                        </view>
                    </view>

                    <nullList v-if="isEmpty" />
                    <view v-if="!noMore && tableData.length > 0" class="load-more">加载更多...</view>
                    <view v-if="noMore && tableData.length > 0" class="load-more">没有更多数据了</view>
                </view>
            </scroll-view>
        </view>

        <!-- 固定在底部的统计信息 -->
        <view class="fixed-stats">
            <text class="stats-text">总计：{{ pagination.total }} 头</text>
        </view>

        <filterPopup @resetSearch="resetSearch" :filterType="filterType" :pickerFilterShow="pickerFilterShow"
            @canel="pickerFilterShow = false" @submitForm="submitForm" :pastureId="pastureId" />
    </view>
</template>

<script>
import CustomNavbar from '../components/CustomNavbar.vue'
import filterPopup from './components/filterPopup.vue'
import nullList from '@/components/null-list/index.vue'
import { livestockPage } from '@/api/pages/livestock/farm'

export default {
    name: 'inventoryDetails',
    components: {
        CustomNavbar,
        filterPopup,
        nullList
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            filterType: 'inventory',
            pickerFilterShow: false,
            // 页面参数
            pastureId: '', // 养殖场ID
            // 列表数据
            tableData: [],
            // 分页配置
            pagination: {
                current: 1,
                pageSize: 10,
                total: 0
            },
            // 筛选参数
            filterParams: {},
            // 列表状态
            refresherState: false,
            noMore: false,
            isEmpty: false,
            loading: false
        }
    },

    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        }
    },

    onLoad(options) {
        if (options.pastureId) {
            this.pastureId = options.pastureId;
        }
        this.loadTableData();
    },

    onReady() {
        if (this.tableData.length === 0) {
            this.loadTableData();
        }
    },

    methods: {
        resetSearch() {
            console.log('resetSearch');
            this.filterParams = {};
            this.pagination.current = 1;
            this.noMore = false;
            this.loadTableData();
        },
        submitForm(val) {
            this.filterParams = { ...val };
            this.pickerFilterShow = false;
            this.pagination.current = 1;
            this.noMore = false;
            this.loadTableData();
        },
        fifterClick(){
            this.pickerFilterShow = true;
        },
        async loadTableData() {
            this.loading = true;
            try {
                const params = {
                    ...this.filterParams,
                    pastureId: this.pastureId,
                    pageNum: this.pagination.current,
                    pageSize: this.pagination.pageSize
                };
                console.log('🚀 API Call - livestockPage params:', JSON.stringify(params));
                const res = await livestockPage(params);

                if (res.code === 200 && res.result) {
                    const processedData = (res.result.list || []).map(item => ({
                        ...item,
                        healthStatusText: this.getHealthStatusText(item.healthStatus),
                        livestockWeight: item.livestockWeight ? parseFloat(item.livestockWeight).toFixed(1) : '0.0'
                    }));

                    const total = parseInt(res.result.total) || 0;
                    this.updateList(processedData, total);
                    console.log('📊 Data loaded - pagination:', JSON.stringify(this.pagination));
                } else {
                    throw new Error(res.message || '获取数据失败');
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                this.updateList([], 0);
            } finally {
                this.loading = false;
            }
        },

        updateList(newList, total) {
            this.pagination.total = total;
            if (this.pagination.current >= 2) {
                this.tableData = this.tableData.concat(newList);
                this.noMore = this.tableData.length >= total;
            } else {
                this.isEmpty = total < 1;
                this.tableData = total >= 1 ? newList : [];
                this.noMore = this.tableData.length >= total;
            }
        },

        scrollToLower() {
            if (this.noMore || this.loading) return;
            this.pagination.current++;
            this.loadTableData();
        },

        bindrefresherrefresh() {
            this.refresherState = true;
            this.pagination.current = 1;
            this.noMore = false;
            this.loadTableData();
            setTimeout(() => {
                this.refresherState = false;
                uni.showToast({ title: '刷新成功', icon: 'none' });
            }, 1000);
        },

        getHealthStatusText(status) {
            const statusMap = {
                1: '健康',
                2: '亚健康',
                3: '生病',
                0: '未知'
            };
            return statusMap[status] || '未知';
        },

        getHealthStatusClass(status) {
            const classMap = {
                1: 'status-healthy',
                2: 'status-warning',
                3: 'status-sick',
                0: 'status-unknown'
            };
            return classMap[status] || 'status-unknown';
        },

        // 列表项点击
        handleRowClick(row, index) {
            console.log('点击行:', row, index);
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/underCareList.scss';

.page-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

.header {
    width: 750rpx;
    height: 250rpx;
    display: flex;
    padding-top: 120rpx;
    box-sizing: border-box;
    position: relative;
    background-color: #fff;

    .static-title {
        left: 30rpx;
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
    }
}

.fifter {
    position: absolute;
    top: 197rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}

.inventory-container {
    flex: 1;
    padding: 20rpx 30rpx 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.inventory-list {
    flex: 1;
    padding: 0;
    box-sizing: border-box;
}

.list-content {
    padding-bottom: 120rpx; // 为底部统计留出空间
}

// 健康状态样式
.status-healthy {
    color: #1CC271;
    font-weight: 500;
}

.status-warning {
    color: #FA8C16;
    font-weight: 500;
}

.status-sick {
    color: #FF4D4F;
    font-weight: 500;
}

.status-unknown {
    color: #999999;
}

// 固定底部统计
.fixed-stats {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1rpx solid #f0f0f0;
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
    z-index: 999;
}

.stats-text {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
}
</style>
